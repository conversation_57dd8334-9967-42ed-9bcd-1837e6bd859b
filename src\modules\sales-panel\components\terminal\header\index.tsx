import { useCancelOrder } from "@/modules/sales-panel/hooks/order/cancel-order.hook";
import { useCreateOrder } from "@/modules/sales-panel/hooks/order/use-create-order.hook";
import { orderInfoAtom } from "@/modules/sales-panel/states/order-info.state";
import { Button } from "@/shared/components/ui/button";
import { useKeyboardShortcut } from "@/shared/hooks/use-keyboard-shortcut";
import { Link } from "@tanstack/react-router";
import { useAtomValue } from "jotai";
import { ArrowLeft, Plus, User, X } from "lucide-react";
import { PendingOrderConsultButton } from "../consult-pending/modal/button";
import { IdentifyPersonButton } from "../person/modal/button";
import { LinkCpf } from "./link-cpf";

export const TerminalHeaderDesktop = ({
	createOrderMutation,
	orderId,
	cancelOrderMutation,
}: {
	createOrderMutation: ReturnType<typeof useCreateOrder>["createOrderMutation"];
	cancelOrderMutation: ReturnType<typeof useCancelOrder>["cancelOrderMutation"];
	orderId: number | null;
}) => {
	const orderInfo = useAtomValue(orderInfoAtom);

	useKeyboardShortcut({
		combination: { key: "c" },
		handler: () => {
			if (orderId) {
				cancelOrderMutation.mutate();
			}
		},
		options: {
			ignoreInputs: false,
			preventDefault: true,
			disabled: !orderId,
		},
	});

	useKeyboardShortcut({
		combination: { key: "a" },
		handler: () => {
			if (!orderId) {
				createOrderMutation.mutate();
			}
		},
		options: {
			ignoreInputs: false,
			preventDefault: true,
			disabled: !!orderId,
		},
	});
	return (
		<header className="w-full z-50 top-0 px-4 py-3 hidden lg:flex justify-between items-center bg-white shadow-lg rounded-2xl border border-gray-100">
			<div className="flex w-full xl:flex-row gap-4 flex-col justify-between items-center h-full">
				<div className="flex w-full xl:w-2/5 items-center gap-3">
					<Button className="bg-gradient-to-r from-[#227989] to-[#2fa2b5] hover:from-[#1e6b7a] hover:to-[#298ba0] shadow-md hover:shadow-lg transition-all duration-300 gap-2 flex text-white px-3 py-2 rounded-xl font-medium text-sm">
						<Link to="/" className="flex items-center gap-2">
							<ArrowLeft size={16} />
							Sair
						</Link>
					</Button>

					{orderId && (orderInfo?.customer || orderInfo?.cpfCnpj) ? (
						<div className="flex items-center gap-3 bg-gradient-to-r from-blue-50 to-cyan-50 px-3 py-2 rounded-xl border border-blue-100 flex-1">
							<div className="flex items-center gap-2">
								<div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
								<h2 className="font-semibold text-lg text-gray-800">Pedido #{orderId}</h2>
								<span className="bg-green-100 border border-green-300 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
									Em aberto
								</span>
							</div>
							{(orderInfo?.customer || orderInfo?.cpfCnpj) && (
								<div className="flex items-center gap-2 bg-white/70 px-2 py-1 rounded-lg">
									<User size={14} className="text-[#227989]" />
									{orderInfo?.customer && <span className="text-sm font-medium text-gray-700">{orderInfo.customer}</span>}
									{orderInfo?.cpfCnpj && <span className="text-xs text-gray-500">• {orderInfo.cpfCnpj}</span>}
								</div>
							)}
						</div>
					) : (
						<div className="bg-gray-50 px-3 py-2 rounded-xl border border-gray-200 flex-1">
							<h2 className="font-medium text-base text-gray-600 flex items-center gap-2">
								<div className="w-2 h-2 bg-gray-400 rounded-full"></div>
								Nenhum pedido em aberto
							</h2>
							<p className="text-xs text-gray-500">Comece uma nova venda para continuar</p>
						</div>
					)}
				</div>
				<div className="md:flex hidden w-full xl:w-3/5 justify-end items-center gap-2">
					{!orderId && (
						<Button
							onClick={() => createOrderMutation.mutate()}
							className="bg-gradient-to-r from-[#227989] to-[#2fa2b5] hover:from-[#1e6b7a] hover:to-[#298ba0] shadow-md hover:shadow-lg flex items-center gap-2 text-white font-semibold px-4 py-2 rounded-xl transition-all duration-300 hover:scale-105 active:scale-100 text-sm"
						>
							<Plus size={16} />
							Adicionar venda
							<span className="text-xs bg-white/90 text-[#227989] px-1.5 py-0.5 rounded font-bold">A</span>
						</Button>
					)}

					<IdentifyPersonButton orderId={orderId} data-testid="identify-person-button" />
					<LinkCpf orderId={orderId!} data-testid="link-cpf-button" />
					<PendingOrderConsultButton data-testid="consult-pending-button" />

					<Button
						disabled={!orderId}
						onClick={() => cancelOrderMutation.mutate()}
						className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 disabled:from-gray-300 disabled:to-gray-400 shadow-md hover:shadow-lg disabled:shadow-none px-3 gap-2 flex text-white py-2 rounded-xl font-medium transition-all duration-300 hover:scale-105 active:scale-100 disabled:scale-100 disabled:cursor-not-allowed text-sm"
					>
						<X size={16} />
						Cancelar venda
						<span className="text-xs bg-white/90 text-red-600 px-1.5 py-0.5 rounded font-bold">C</span>
					</Button>
				</div>
			</div>
		</header>
	);
};
